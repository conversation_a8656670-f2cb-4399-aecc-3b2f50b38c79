import os
from abc import ABC, abstractmethod
from typing import Optional, Any
from langchain_community.chat_models import ChatLlamaCpp
from langchain_core.messages import SystemMessage, HumanMessage
import streamlit as st

from src.code_review.core import Utils

# Debug mode flag
DEBUG_MODE = os.getenv("DEBUG_MODE", "false").lower() == "true"

_cached_llm = None

@st.cache_resource
def load_local_llm():
    """Load and cache the local LLM instance."""
    global _cached_llm
    if _cached_llm is not None:
        return _cached_llm

    model_path = "./models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf"

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found at {model_path}")

    try:
        _cached_llm = ChatLlamaCpp(
            model_path=model_path,
            n_ctx=9192,  # Context window
            n_threads=4,  # CPU threads
            temperature=0.1,  # Low temperature for consistent code review
            max_tokens=2048,  # Max tokens
            verbose=False,  # Disable verbose logging
            stop=["</s>", "\n\n"],  # Stop sequences
            n_batch=512,  # Batch size
            f16_kv=True,  # Enable 16-bit key/value cache
            use_mlock=True,  # Lock model in RAM
            n_gpu_layers=0,  # CPU only for now
            seed=42,  # Fixed seed for reproducibility
        )
        return _cached_llm
    except Exception as e:
        raise RuntimeError(f"Failed to load LLM model: {str(e)}")


class BaseAgent(ABC):
    """Base class for all code review agents."""
    
    def __init__(self, llm: Optional[Any] = None):
        """Initialize the agent with an optional LLM instance."""
        self.llm = llm or load_local_llm()
    
    @property
    @abstractmethod
    def system_prompt(self) -> str:
        """Return the system prompt specific to this agent."""
        pass
    
    def invoke(self, user_prompt: str) -> str:
        """Invoke the agent with a user prompt."""
        try:
            messages = [
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=user_prompt)
            ]

            Utils.debug_print(f"[DEBUG] Invoking LLM with {len(user_prompt)} chars...")

            response = self.llm.invoke(messages)
            result = response.content if hasattr(response, 'content') else str(response)

            Utils.debug_print(f"[DEBUG] LLM response length: {len(result)} chars")

            return result
        except Exception as e:
            Utils.debug_print(f"[DEBUG] LLM invoke error: {e}")
            raise
    
    @abstractmethod
    def process(self, **kwargs) -> dict:
        """Process the input and return structured output."""
        pass
