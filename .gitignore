# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
# .python-version

# pipenv
#Pipfile.lock

# poetry
#poetry.lock

# pdm
#pdm.lock
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/
*.iml
*.ipr

# VS Code
.vscode/
*.code-workspace
.history/
*.vsix

# Model files (GGUF and other large model files)
*.gguf
*.bin
*.safetensors
*.pt
*.pth
*.onnx
*.tflite
*.h5
*.pkl
*.pickle

# Model directories
models/
checkpoints/
weights/

# Data files
data/
datasets/
*.csv
*.json
*.jsonl
*.parquet
*.feather

# Logs and temporary files
logs/
*.log
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.lnk

# Streamlit
.streamlit/

# LangChain/LangSmith
.langchain/
.langsmith/

# FAISS index files
*.faiss
*.index
vector_store/
embeddings/

# Jupyter notebooks (optional)
# *.ipynb

# Local configuration files
config.local.json
settings.local.json

# API keys and secrets
*.key
*.pem
*.p12
secrets/
credentials/

# Cache directories
.cache/
cache/

# Test outputs
test_outputs/
test_results/

# Temporary Python files
*.pyc
*.pyo
*.pyd

# Virtual environment activation scripts
Scripts/
pyvenv.cfg

# Local development files
.local/
local/

# Backup files
*.bak
*.backup
*~

# Editor temporary files
*.swp
*.swo
*~

# Package lock files (uncomment if needed)
# package-lock.json
# yarn.lock

# Node modules (if using any JS tools)
node_modules/

# Python wheel files
*.whl

# Coverage reports
.coverage.*
htmlcov/

# Pytest
.pytest_cache/

# Tox
.tox/

# Documentation builds
docs/_build/
docs/build/

# Profiling data
*.prof

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration overrides
config.override.*
settings.override.*

# IDE files
*.sublime-project
*.sublime-workspace

# MacOS
.AppleDouble
.LSOverride

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Temporary folders
tmp/
temp/
