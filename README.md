# Bot Review Assistant

An intelligent code review assistant powered by <PERSON><PERSON><PERSON><PERSON>, LangGraph, and local LLM models. This tool analyzes pull request diffs and provides comprehensive feedback on code quality, naming conventions, syntax, and logic.

## Features

- **Multi-language Support**: Automatically detects programming language
- **AST-based Code Chunking**: Uses Tree-sitter for intelligent code parsing and chunking
- **Logic Review**: Analyzes code logic and provides suggestions
- **Interactive UI**: Clean Streamlit interface with organized results
- **RAG Architecture**: Uses FAISS vector store for context-aware reviews
- **API Server**: LangGraph Platform CLI for production deployment

## Quick Start

### Prerequisites

- Python 3.13
- At least 8GB RAM (for local LLM)
- GGUF model file (DeepSeek-Coder recommended)
- **CMake** (required for llama-cpp-python compilation)
- **Node.js & npm** (required for tree-sitter grammars)
- **Git** (required for cloning grammar repositories)

#### Installing CMake on Windows

For Windows users, install CMake using Chocolatey package manager:

**Step 1**: Install Chocolatey (if not already installed)
```powershell
# Run as Administrator in PowerShell
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager].SecurityProtocol = [System.Net.ServicePointManager].SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

**Step 2**: Install CMake with Chocolatey
```powershell
# Run as Administrator
choco install cmake
```

**Step 3**: Verify installation
```bash
cmake --version
```

#### Installing Tree-sitter CLI(v0.20.8)

**Step 1**: Install Node.js (if not already installed)
```bash
# Download from https://nodejs.org/ or use package manager
```

**Step 2**: Install tree-sitter-cli globally
```bash
npm install -g tree-sitter-cli
```

**Step 3**: Verify installation
```bash
tree-sitter --version
```

> **Note**: CMake and tree-sitter-cli are essential for the setup. CMake enables GGUF model support, while tree-sitter-cli enables AST-based code parsing.

### Installation

**Step 1**: Setup virtual environment
```bash
cd bot-assistance
python -m venv venv
source venv/bin/activate.bat  # On Windows
```

**Step 2**: Install dependencies
```bash
pip install -r requirements.txt
```

**Step 3**: Setup Tree-sitter parsers
```bash
python setup_parser.py
```

**Step 4**: Download model
- Download `DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf`
- Download `all-MiniLM-L6-v2-Q4_K_M.gguf`
- Place it in `./models/` directory

**Step 5**: Configure environment
```bash
cp .env.example .env
```
**Then filled .env*

**Step 6**: Run the application
```bash
streamlit run main.py
```

## LangGraph Platform CLI Setup

For API deployment and production use:

### 1. Install LangGraph CLI
```bash
pip install --upgrade "langgraph-cli[inmem]"
```

### 2. Create LangGraph app (if starting fresh)
```bash
langgraph new path/to/your/app --template new-langgraph-project-python
cd path/to/your/app
pip install -e .
```

### 3. Setup langgraph.json
Create `langgraph.json`

### 4. Run API Server
```bash
langgraph dev
```

Server will be available at:
- **API**: http://localhost:2024
- **Docs**: http://localhost:2024/docs
- **Studio**: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024

## Usage

1. **Open the web interface** (usually http://localhost:8501)
2. **Paste your PR diff** in the text area
3. **Click "Review Code"** to start analysis
4. **View organized results** in different tabs:
   - Summary: Overall assessment
   - Logic: Logic review and suggestions

## Architecture

The Bot Review Assistant follows a **RAG-based architecture** with **LangGraph state graphs** for orchestrating the code review workflow. The system combines local LLM inference with vector-based retrieval for context-aware code analysis.

### Project Structure

```
bot-assistance/
├── src/                              # Source code
│   └── code_review/                  # Main package
│       ├── __init__.py               # Package exports
│       ├── agents/                   # Specialized review agents
│       │   ├── __init__.py
│       │   ├── base.py               # BaseAgent class + LLM management
│       │   ├── logic.py              # LogicAgent - logic analysis
│       │   └── summary.py            # SummaryAgent - feedback synthesis
│       ├── pipeline/                 # Workflow orchestration
│       │   ├── __init__.py
│       │   ├── nodes.py              # LangGraph node functions
│       │   └── workflow.py           # Main ReviewPipeline class
│       ├── core/                     # Core utilities
│       │   ├── __init__.py
│       │   ├── tools.py              # Code analysis tools
│       │   ├── vector_store.py       # Vector operations
│       │   └── chunkers/             # AST-based code chunkers
│       │       ├── __init__.py
│       │       ├── base_chunker.py   # Base chunker with Tree-sitter
│       │       ├── diff_chunker.py   # Diff parsing and chunking
│       │       ├── chunker_factory.py # Language-specific chunker factory
│       │       ├── js_chunker.py     # JavaScript/TypeScript chunker
│       │       ├── angular_chunker.py # Angular-specific chunker
│       │       ├── react_chunker.py  # React-specific chunker
│       │       └── csharp_chunker.py # C# chunker
├── build/                            # Compiled Tree-sitter parsers
│   └── my-languages.dll/.so          # Language parser library
├── models/                           # Model files only
│   ├── DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf
│   └── all-MiniLM-L6-v2-Q4_K_M.gguf
├── setup_parser.py                   # Tree-sitter parser setup script
├── main.py                           # Streamlit web interface
├── test_agents.py                    # Agent and pipeline tests
├── requirements.txt                  # Dependencies
└── README.md                         # This documentation
```

### Agent-Based Workflow

The system uses **specialized AI agents** orchestrated by **LangGraph** in a sequential workflow:

1. **Language Detection** → Identifies programming language from diff
2. **AST-based Code Chunking** → Tree-sitter parsing and intelligent chunking of code changes
3. **Structure Scanning** → Extracts classes, functions, variables using AST
4. **LogicAgent** → Analyzes code logic and provides suggestions
5. **SummaryAgent** → Creates comprehensive review summary

### Specialized Agents

#### **LogicAgent**
- Focus on logic correctness and code quality
- Performance and security considerations
- Code smells and anti-patterns
- Edge cases and error scenarios
- Best practices and architectural patterns

#### **SummaryAgent**
- Focus on synthesis and communication
- Natural language summaries
- Prioritized feedback
- Actionable recommendations
- Overall code quality assessment

### AST-Based Code Chunking

The system uses **Tree-sitter** for intelligent code parsing and chunking:

#### **Supported Languages**
- **JavaScript/TypeScript**: Full ES6+ support with JSX/TSX
- **Angular**: Template and component-specific parsing
- **React**: JSX and component structure analysis
- **C#**: .NET and ASP.NET Core code analysis

#### **Chunking Strategy**
- **Function-level chunks**: Each function becomes a separate chunk
- **Class-level chunks**: Class methods and properties grouped together
- **Import/export chunks**: Module dependencies analyzed separately
- **Template chunks**: Angular/React templates parsed independently

#### **Tree-sitter Setup**
The `setup_parser.py` script automatically:
1. Clones language grammar repositories
2. Compiles Tree-sitter parsers for each language
3. Builds a unified language library (`my-languages.dll/.so`)
4. Supports multiple language variants (TypeScript, Angular, etc.)

### RAG Components

- **Vector Store**: FAISS-based vector database for code context
- **Embeddings**: HuggingFace sentence-transformers (all-MiniLM-L6-v2)
- **Chunking Strategy**: AST-based code segmentation for better context
- **Retrieval**: Context-aware code snippet retrieval for enhanced reviews

### Technology Stack

- **LangChain/LangGraph**: Workflow orchestration and LLM integration
- **llama-cpp-python**: Local LLM inference (DeepSeek-Coder)
- **Tree-sitter**: AST-based code parsing and analysis
- **Streamlit**: Interactive web interface
- **FAISS**: Vector similarity search
- **HuggingFace**: Embeddings and model management

#### Web Interface
```bash
streamlit run main.py
```

## TODO

- [x] Setup API with LangGraph platform - [docs](https://langchain-ai.github.io/langgraph/tutorials/langgraph-platform/local-server/)
- [ ] Combine with TYI's code
- [ ] Optimize agent system prompts based on usage patterns
- [ ] Add more specialized agents if needed
- [ ] Implement parallel agent processing
- [ ] POC git API integration
- [ ] Performance monitoring and optimization
  - Advance:
    - [ ] Full codebase context: AI reviews changes with awareness of related files, dependencies, and architectural impact.
    - [ ] In-line comments and suggestions: Developers receive actionable feedback directly in the pull request, reducing back-and-forth. (Currently)
    - [x] Natural language summaries: AI generates concise summaries of PRs, making it easier for reviewers to understand the scope and intent (Currently)
    - [ ] Customizable review focus: Teams can specify which types of changes or issues the AI should prioritize (UI)
    - [ ] Support for multiple languages: Modern tools handle CSharp, Java, JavaScript, and more (Currently)
      - Example Code Review Flow:
        - Developer opens a pull request, then clicks the button to trigger.
        - Analyzes the PR, referencing the entire codebase graph.
        - Leaves in-line comments highlighting bugs, architectural issues, and style violations.
        - Expert (human) review once again to validate and add missing points.