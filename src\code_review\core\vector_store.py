from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import LlamaCppEmbeddings
import re
import streamlit as st

from .utils import Utils
from src.code_review.core.chunkers import get_context_chunker, get_diff_chunker

# Cache for embedding model
_cached_embeddings = None

def chunk_logic(diff_text):
    Utils.debug_print("[DEBUG] diff_text input:\n", diff_text[:1000], "...\n---END---")
    documents = []
    file_paths = []
    file_pattern = re.compile(r"^((diff --git a/.*? b/.*?)|(### File: '(.+?)'))$", re.MULTILINE)
    matches = list(file_pattern.finditer(diff_text))
    Utils.debug_print(f"[DEBUG] Found {len(matches)} file matches in diff.")
    for idx, match in enumerate(matches):
        start = match.start()
        end = matches[idx + 1].start() if idx + 1 < len(matches) else len(diff_text)
        block = diff_text[start:end]
        # Get file_path
        file_path = None
        if match.group(4):
            file_path = match.group(4)
        else:
            m = re.search(r'diff --git a/(.*?) b/', match.group(0))
            if m:
                file_path = m.group(1)
        if not file_path:
            Utils.debug_print(f"[DEBUG] No file_path found for block {idx}")
            continue
        file_content = block
        diff_lines = set()
        # hunk start with @@
        hunk_pattern = re.compile(r'^@@.*$', re.MULTILINE)
        hunk_matches = list(hunk_pattern.finditer(block))
        for hunk_idx, hunk in enumerate(hunk_matches):
            hunk_start = hunk.end()
            hunk_end = hunk_matches[hunk_idx + 1].start() if hunk_idx + 1 < len(hunk_matches) else len(block)
            hunk_lines = block[hunk_start:hunk_end].splitlines()
            # If it's a standard git diff, parse the number of lines, otherwise get all lines +
            hunk_header = hunk.group(0)
            m = re.match(r'^@@\s+-(\d+),?(\d+)?\s+\+(\d+),?(\d+)?\s+@@', hunk_header)
            if m:
                new_start = int(m.group(3))
                new_line_num = new_start
                for line in hunk_lines:
                    if line.startswith('@@'):
                        break
                    if line.startswith('+') and not line.startswith('+++'):
                        diff_lines.add(new_line_num)
                        new_line_num += 1
                    elif line.startswith('-') and not line.startswith('---'):
                        continue
                    else:
                        new_line_num += 1
            else:
                # Bitbucket-style: get all lines +
                for i, line in enumerate(hunk_lines):
                    if line.startswith('@@'):
                        break
                    if line.startswith('+') and not line.startswith('+++'):
                        diff_lines.add(i+1)
        diff_lines = sorted(diff_lines)
        Utils.debug_print(f"[DEBUG] file_path: {file_path}, diff_lines: {diff_lines}")
        chunker = get_context_chunker(file_path)
        if chunker is None:
            Utils.debug_print(f"[DEBUG] No chunker found for file: {file_path}")
            continue
        try:
            docs = chunker.chunk_code(file_content, file_path, diff_lines)
            Utils.debug_print(f"[DEBUG] {file_path}: chunker returned {len(docs)} documents.")
        except Exception as e:
            Utils.debug_print(f"[DEBUG] Error chunking {file_path}: {e}")
            docs = []
        if not docs:
            Utils.debug_print(f"[DEBUG] No documents returned for file: {file_path}")
        documents.extend(docs)
        file_paths.append(file_path)
    Utils.debug_print(f"[DEBUG] Total file_paths: {file_paths}")
    Utils.debug_print(f"[DEBUG] Total documents: {len(documents)}")
    return documents, file_paths

def chunk_diff(diff_text: str):
    return get_diff_chunker().chunk_diff(diff_text)

@st.cache_resource
def load_embedding_model():
    """Load and cache the embedding model instance."""
    global _cached_embeddings
    if _cached_embeddings is not None:
        return _cached_embeddings

    embedding_model_path ="./models/all-MiniLM-L6-v2-Q4_K_M.gguf"
    try:
        _cached_embeddings = LlamaCppEmbeddings(
            model_path=embedding_model_path,
            n_ctx=1024,  # Increased context window (was 512)
            n_threads=4,  # Increased CPU threads (was 2)
            n_batch=128,  # Increased batch size (was 64)
            n_gpu_layers=0,  # Number of model layers to offload to GPU (set > 0 if using GPU)
            n_parts=-1,  # Number of model parts to split into (default -1 means auto)
            seed=42,  # Random seed for reproducibility (use -1 for random behavior)
            f16_kv=True,  # Use 16-bit key/value cache if supported (saves memory)
            logits_all=False,  # Whether to return logits for all tokens (not needed for embedding)
            vocab_only=False,  # Load only vocabulary without weights (False = load full model)
            use_mlock=True,  # Lock the model in RAM to prevent swapping
            device=None,  # Device override (None = auto-select, can set 'cpu' or 'cuda')
            verbose=False,  # Disable verbose logs
        )
        Utils.debug_print(f"Loaded embedding model: {embedding_model_path}")
        return _cached_embeddings
    except Exception as e:
        Utils.debug_print(f"Failed to load GGUF embedding model: {e}")
        return None

def build_vector_store(documents):
    if not documents:
        return None

    embeddings = load_embedding_model()
    if embeddings is None:
        Utils.debug_print("No embedding model available. Vector store disabled.")
        return None

    try:
        return FAISS.from_documents(documents, embeddings)
    except Exception as e:
        Utils.debug_print(f"Failed to build vector store: {e}")
        return None

def search_similar_code(vector_store, query, k=3):
    if vector_store is None:
        return []
    
    try:
        return vector_store.similarity_search(query, k=k)
    except Exception as e:
        Utils.debug_print(f"Vector search failed: {e}")
        return []
