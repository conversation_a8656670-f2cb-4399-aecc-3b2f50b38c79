#!/usr/bin/env python3
"""
Test script for the improved LogicAgent to verify it can detect real issues like N+1 queries
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

from langchain.schema import Document
from src.code_review.agents.logic import LogicAgent

# Load environment variables
load_dotenv(".env")

# Enable debug mode
import os
os.environ["DEBUG_MODE"] = "true"

def test_n_plus_one_detection():
    """Test if LogicAgent can detect N+1 query pattern"""
    
    # Sample C# code with N+1 query issue
    csharp_code_with_n_plus_one = """
    public IActionResult GetProductReviews(int id) {
        var product = _productRepository.GetById(id);
        var reviews = new List<Review>();
        foreach (var reviewId in product.ReviewIds) {
            reviews.Add(_reviewRepository.GetById(reviewId));
        }
        return Ok(reviews);
    }
    """
    
    # Create a document that simulates AST chunk
    chunk_doc = Document(
        page_content=csharp_code_with_n_plus_one,
        metadata={
            'file_path': 'Backend/Controllers/ProductController.cs',
            'chunk_type': 'method',
            'name': 'GetProductReviews',
            'diff_lines': [3, 4, 5, 6, 7, 8, 9, 10],
            'start_line': 3,
            'end_line': 10
        }
    )
    
    # Mock file contents
    file_contents = {
        'Backend/Controllers/ProductController.cs': """using Microsoft.AspNetCore.Mvc;
namespace Backend.Controllers {
    public IActionResult GetProductReviews(int id) {
        var product = _productRepository.GetById(id);
        var reviews = new List<Review>();
        foreach (var reviewId in product.ReviewIds) {
            reviews.Add(_reviewRepository.GetById(reviewId));
        }
        return Ok(reviews);
    }
}"""
    }
    
    # Initialize LogicAgent
    logic_agent = LogicAgent()
    
    print("Testing LogicAgent with N+1 query pattern...")
    print("=" * 60)
    
    try:
        # Process the chunk
        result = logic_agent.process([chunk_doc], file_contents)
        
        print("Result:")
        print(f"Total files reviewed: {result.get('total_files_reviewed', 0)}")
        print(f"Total chunks reviewed: {result.get('total_chunks_reviewed', 0)}")
        
        file_reviews = result.get('file_reviews', [])
        if file_reviews:
            for file_review in file_reviews:
                print(f"\nFile: {file_review.get('file_path', 'Unknown')}")
                line_feedback = file_review.get('line_feedback', {})
                
                if line_feedback:
                    print("Line Feedback:")
                    for line, feedback in line_feedback.items():
                        print(f"  Line {line}: {feedback}")
                else:
                    print("  No line feedback provided")
                
                key_issues = file_review.get('key_issues_to_review', [])
                if key_issues:
                    print("Key Issues:")
                    for issue in key_issues:
                        print(f"  - {issue}")
                
                security_concerns = file_review.get('security_concerns', '')
                if security_concerns:
                    print(f"Security Concerns: {security_concerns}")
                
                score = file_review.get('score', 'N/A')
                print(f"Score: {score}")
        else:
            print("No file reviews found")
            
        print("\n" + "=" * 60)
        print("Expected: Should detect N+1 query in line 7 where _reviewRepository.GetById() is called inside foreach loop")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

def test_good_code_no_false_positive():
    """Test that LogicAgent doesn't create false positives for good code"""

    # Sample C# code without issues
    good_csharp_code = """
    public IActionResult GetProductReviews(int id) {
        var reviews = _reviewRepository.GetByProductId(id);
        return Ok(reviews);
    }
    """

    # Create a document that simulates AST chunk
    chunk_doc = Document(
        page_content=good_csharp_code,
        metadata={
            'file_path': 'Backend/Controllers/ProductController.cs',
            'chunk_type': 'method',
            'name': 'GetProductReviews',
            'diff_lines': [3, 4, 5, 6],
            'start_line': 3,
            'end_line': 6
        }
    )

    # Mock file contents
    file_contents = {
        'Backend/Controllers/ProductController.cs': """using Microsoft.AspNetCore.Mvc;
namespace Backend.Controllers {
    public IActionResult GetProductReviews(int id) {
        var reviews = _reviewRepository.GetByProductId(id);
        return Ok(reviews);
    }
}"""
    }

    # Initialize LogicAgent
    logic_agent = LogicAgent()

    print("\nTesting LogicAgent with good code (should have minimal feedback)...")
    print("=" * 60)

    try:
        # Process the chunk
        result = logic_agent.process([chunk_doc], file_contents)

        file_reviews = result.get('file_reviews', [])
        if file_reviews:
            for file_review in file_reviews:
                print(f"\nFile: {file_review.get('file_path', 'Unknown')}")
                line_feedback = file_review.get('line_feedback', {})

                if line_feedback:
                    print("Line Feedback:")
                    for line, feedback in line_feedback.items():
                        print(f"  Line {line}: {feedback}")
                else:
                    print("  No line feedback provided (GOOD - no issues found)")

                key_issues = file_review.get('key_issues_to_review', [])
                if key_issues:
                    print("Key Issues:")
                    for issue in key_issues:
                        print(f"  - {issue}")
                else:
                    print("  No key issues (GOOD)")

                score = file_review.get('score', 'N/A')
                print(f"Score: {score}")

        print("\n" + "=" * 60)
        print("Expected: Should have minimal or no feedback since this is good code")

    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_n_plus_one_detection()
    test_good_code_no_false_positive()
