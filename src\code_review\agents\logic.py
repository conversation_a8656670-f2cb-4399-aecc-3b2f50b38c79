from typing import List, Dict, Any, Optional
from .base import BaseAgent
from langchain.schema import Document
from ..core import Utils
from .language_contexts import get_language_context, get_performance_patterns


class LogicAgent(BaseAgent):
    @property
    def system_prompt(self) -> str:
        return """
You are an expert code reviewer. Focus on finding REAL problems:
- Bugs that will cause runtime errors
- Performance issues (N+1 queries, inefficient algorithms)
- Security vulnerabilities
- Critical best practice violations

ONLY provide feedback for lines with actual problems. Skip standard code, declarations, and correct implementations.
Always respond in valid JSON format.
"""

    def process(self, chunked_documents: List[Document],
                file_contents: Dict[str, str] = None) -> Dict[str, Any]:
        if not chunked_documents:
            return {
                "line_feedback": {},
                "key_issues_to_review": [],
                "security_concerns": [],
                "score": [],
                "relevant_tests": [],
                "overall_quality": "No files to review",
                "total_files_reviewed": 0,
                "total_chunks_reviewed": 0
            }

        file_reviews = []
        chunks_by_file = self._group_chunks_by_file(chunked_documents)

        for file_path, chunks in chunks_by_file.items():
            file_review = self._review_file_chunks(file_path, chunks, file_contents)
            if file_review:
                file_reviews.append(file_review)

        review_objects = []
        for file_review in file_reviews:
            Utils.debug_print(f"[DEBUG] Test - File review object: {file_review}")
            file_obj = {
                "file_path": file_review.get("file_path"),
                "diff_lines": file_review.get("diff_lines", []),
                "line_feedback": {},
                "key_issues_to_review": [],
                "security_concerns": [],
                "score": [],
                "relevant_tests": [],
            }
            for chunk in file_review.get("chunk_reviews", []):
                if isinstance(chunk, str):
                    chunk = Utils.parse_json_from_response(chunk) or {}
                if "review" in chunk:
                    review = chunk["review"]
                else:
                    continue
                if isinstance(review, str):
                    review = Utils.parse_json_from_response(review) or {}
                Utils.debug_print(f"[DEBUG] Review object: {review}")
                if (
                        isinstance(review, dict)
                        and all(isinstance(k, str) and k.isdigit() for k in review.keys())
                        and not any(
                    k in review for k in ["key_issues_to_review", "security_concerns", "score", "relevant_tests"])
                ):
                    review = {"line_feedback": review}
                lf = review.get("line_feedback", {})
                if lf:
                    file_obj["line_feedback"].update(lf)
                issues = review.get("key_issues_to_review", [])
                Utils.debug_print(f"[DEBUG] key_issues_to_review: {issues}")
                if isinstance(issues, list) and issues:
                    file_obj["key_issues_to_review"].extend(issues)
                elif isinstance(issues, dict):
                    file_obj["key_issues_to_review"].append(issues)
                sec = review.get("security_concerns", None)
                Utils.debug_print(f"[DEBUG] security_concerns: {sec}")
                if sec is not None:
                    file_obj["security_concerns"].append(sec)
                sc = review.get("score", None)
                Utils.debug_print(f"[DEBUG] score: {sc}")
                if sc is not None:
                    file_obj["score"].append(sc)
                tests = review.get("relevant_tests", None)
                Utils.debug_print(f"[DEBUG] relevant_tests: {tests}")
                if tests is not None:
                    file_obj["relevant_tests"].append(tests)
            review_objects.append(file_obj)
        Utils.debug_print(f"[DEBUG] Final review_objects: {review_objects}")
        return {
            "file_reviews": review_objects,
            "total_files_reviewed": len(file_reviews),
            "total_chunks_reviewed": len(chunked_documents)
        }

    @staticmethod
    def _group_chunks_by_file(chunked_documents: List[Document]) -> Dict[str, List[Document]]:
        chunks_by_file = {}
        for doc in chunked_documents:
            file_path = doc.metadata.get('file_path', 'unknown')
            if file_path not in chunks_by_file:
                chunks_by_file[file_path] = []
            chunks_by_file[file_path].append(doc)
        return chunks_by_file

    def _review_file_chunks(self, file_path: str, chunks: List[Document],
                            file_contents: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
        if not chunks:
            return None
        all_diff_lines = []
        chunk_reviews = []
        for chunk in chunks:
            chunk_review = self._review_single_chunk(chunk, file_contents)
            if chunk_review:
                chunk_reviews.append(chunk_review)
                all_diff_lines.extend(chunk.metadata.get('diff_lines', []))
        all_diff_lines = sorted(list(set(all_diff_lines)))
        Utils.debug_print(f"[DEBUG] All diff lines: {all_diff_lines}")
        line_feedback = self._aggregate_line_feedback(chunk_reviews)
        Utils.debug_print(f"[DEBUG] Line feedback: {line_feedback}")
        return {
            "file_path": file_path,
            "diff_lines": all_diff_lines,
            "chunk_reviews": chunk_reviews,
            "line_feedback": line_feedback,
        }

    def _review_single_chunk(self, chunk: Document, file_contents: Dict[str, str] = None) -> Dict[str, Any]:
        metadata = chunk.metadata
        chunk_type = metadata.get('chunk_type', 'unknown')
        chunk_name = metadata.get('name', 'unknown')
        diff_lines = metadata.get('diff_lines', [])
        file_path = metadata.get('file_path', 'unknown')

        code_type = Utils.detect_code_type(file_path)
        language_context = get_language_context(code_type)
        performance_patterns = get_performance_patterns(code_type)

        context_info = self._prepare_chunk_context(chunk, file_contents)

        full_code_context = ""
        if file_contents and file_path in file_contents:
            full_code_context = file_contents[file_path]

        # Enhanced and clearer prompt combining both approaches
        user_prompt = f"""
You are PR-Reviewer, a senior and expert code reviewer and principal engineer. Your ONLY job is to find REAL problems:
- Bugs that will cause runtime errors
- Performance issues (N+1 queries, inefficient algorithms)
- Security vulnerabilities
- Critical best practice violations

STRICT RULES:
- DO NOT describe what the code does.
- DO NOT give feedback for function headers, braces, variable declarations, or lines with no issues.
- DO NOT suggest improvements unless there is a real problem.
- If you cannot find a real issue, OMIT that line from your feedback.

BAD EXAMPLES (do NOT do this):
- "Ensure the method signature is correct."
- "Initialize the variable before use."
- "Add error handling."
- "Return the result."
- "This line fetches data from the repository."
- "This is a function header."

GOOD EXAMPLES:
- "N+1 query detected: Each review is fetched individually inside the loop. This can cause severe performance issues for large datasets. Use batch fetching instead."
- "Potential SQL injection vulnerability: User input is concatenated directly into the query string. Use parameterized queries."

Before returning your output, double-check that:
- You only provide feedback for lines with real issues.
- You do NOT describe code or give generic suggestions.
- All feedback is actionable and expert-level.
If you find any feedback that violates these rules, REMOVE it.

If you provide feedback for lines with no real issue, or just describe code, your review will be considered low quality.

LANGUAGE-SPECIFIC CONTEXT FOR {code_type}:
{language_context}

PERFORMANCE PATTERNS TO DETECT FOR {code_type}:
{performance_patterns}

Below is the code diff for review:
======
## File: '{file_path}'
{chunk.page_content}
======

Context information:
------
{context_info}
------

CHANGED LINES TO REVIEW: {sorted(diff_lines)}

Full file content:
------
{full_code_context}
------

Output format (MUST be valid JSON):
{{
  "review": {{
    "line_feedback": {{ ... }},
    "key_issues_to_review": [ ... ],
    "security_concerns": "...",
    "score": ...,
    "relevant_tests": "..."
  }}
}}

FOCUS ON LINES: {sorted(diff_lines)}
Only provide feedback for lines that have actual problems.
"""

        try:
            Utils.debug_print(f"REVIEWING LINES: {sorted(diff_lines)}")
            Utils.debug_print(f"USER PROMPT SENT TO LLM:\n{user_prompt[:1000]}...\n[TRUNCATED]" if len(
                user_prompt) > 1000 else f"USER PROMPT SENT TO LLM:\n{user_prompt}")

            response = self.invoke(user_prompt).strip()
            Utils.debug_print(f"RAW LLM RESPONSE: {response}")

            parsed_result = Utils.parse_json_from_response(response)
            Utils.debug_print(f"[DEBUG] parsed_result after parse: {parsed_result}")

            if parsed_result and 'review' in parsed_result:
                review_obj = parsed_result['review']

                # Validate that feedback is provided for correct lines
                feedback_lines = set(review_obj.get('line_feedback', {}).keys())
                expected_lines = set(str(line) for line in diff_lines)

                Utils.debug_print(f"Expected lines: {expected_lines}")
                Utils.debug_print(f"Feedback lines: {feedback_lines}")

                missing_lines = expected_lines - feedback_lines
                if missing_lines:
                    Utils.debug_print(f"INFO: No issues found for lines: {missing_lines}")

                # Ensure all required fields are present with reasonable defaults
                review_obj.setdefault('key_issues_to_review', [])
                review_obj.setdefault('security_concerns', "No security concerns identified")
                review_obj.setdefault('score', 85)  # Default good score
                review_obj.setdefault('relevant_tests', "Add unit tests for edge cases and error handling")

                # Convert string values to proper types if needed
                if isinstance(review_obj.get('score'), str):
                    try:
                        review_obj['score'] = int(review_obj['score'])
                    except ValueError:
                        review_obj['score'] = 85

                # Ensure key_issues_to_review is a list
                if not isinstance(review_obj.get('key_issues_to_review'), list):
                    if review_obj.get('key_issues_to_review'):
                        review_obj['key_issues_to_review'] = [str(review_obj['key_issues_to_review'])]
                    else:
                        review_obj['key_issues_to_review'] = []

                # Handle backward compatibility with old format
                if (
                        all(isinstance(k, str) and k.isdigit() for k in review_obj.keys())
                        and not any(
                    k in review_obj for k in ["key_issues_to_review", "security_concerns", "score", "relevant_tests"])
                ):
                    review_obj = {"line_feedback": review_obj}

                # Add metadata
                review_obj["chunk_type"] = chunk_type
                review_obj["chunk_name"] = chunk_name
                review_obj["diff_lines"] = diff_lines
                review_obj["start_line"] = metadata.get('start_line')
                review_obj["end_line"] = metadata.get('end_line')

                parsed_result['review'] = review_obj

                return parsed_result
            else:
                Utils.debug_print(f"LogicAgent: Failed to parse JSON for chunk {chunk_name}")
                return self._create_fallback_chunk_review(chunk)

        except Exception as e:
            Utils.debug_print(f"LogicAgent: Failed to process chunk {chunk_name}: {str(e)}")
            return self._create_fallback_chunk_review(chunk)

    def _prepare_chunk_context(self, chunk: Document, file_contents: Dict[str, str] = None) -> str:
        metadata = chunk.metadata
        context_parts = []

        # Basic metadata
        if metadata.get('parent'):
            context_parts.append(f"Parent class: {metadata['parent']}")
        if metadata.get('parameters'):
            context_parts.append(f"Parameters: {', '.join(metadata['parameters'])}")
        if metadata.get('return_type'):
            context_parts.append(f"Return type: {metadata['return_type']}")
        if metadata.get('access_modifier'):
            context_parts.append(f"Access: {metadata['access_modifier']}")

        file_path = metadata.get('file_path')
        diff_lines = metadata.get('diff_lines', [])
        if file_contents and file_path in file_contents:
            context_lines = self._get_smart_context_lines(
                file_contents[file_path], diff_lines
            )
            if context_lines:
                context_parts.append("Context:\n" + "\n".join(context_lines))

        return "\n".join(context_parts) if context_parts else "No context available"

    @staticmethod
    def _get_smart_context_lines(file_content: str, diff_lines: List[int], context_size: int = 3) -> List[str]:
        if not diff_lines:
            return []
        lines = file_content.split('\n')
        min_line = max(1, min(diff_lines) - context_size)
        max_line = min(len(lines), max(diff_lines) + context_size)
        context_lines = []
        for i in range(min_line, max_line + 1):
            prefix = ">>> " if i in diff_lines else "    "
            context_lines.append(f"{prefix}{i:4d}: {lines[i-1]}")
        return context_lines

    @staticmethod
    def _create_fallback_chunk_review(chunk: Document) -> Dict[str, Any]:
        metadata = chunk.metadata
        return {
            "review": {
                "chunk_type": metadata.get('chunk_type', 'unknown'),
                "chunk_name": metadata.get('name', 'unknown'),
                "line_feedback": {},
                "key_issues_to_review": ["Review failed - unable to process chunk"],
                "security_concerns": "Unable to analyze security concerns",
                "score": 0,
                "relevant_tests": "Unable to provide test recommendations",
                "diff_lines": metadata.get('diff_lines', []),
                "start_line": metadata.get('start_line'),
                "end_line": metadata.get('end_line')
            }
        }

    @staticmethod
    def _aggregate_line_feedback(chunk_reviews: List[Dict[str, Any]]) -> Dict[int, Any]:
        """Aggregate line feedback from multiple chunks, handling both string and dict feedback"""
        line_feedback = {}

        for review in chunk_reviews:
            if isinstance(review, dict) and 'review' in review:
                chunk_line_feedback = review['review'].get('line_feedback', {})
            else:
                chunk_line_feedback = review.get('line_feedback', {})

            for line_num_str, feedback in chunk_line_feedback.items():
                try:
                    line_num = int(line_num_str)
                    if line_num not in line_feedback:
                        line_feedback[line_num] = []
                    line_feedback[line_num].append(feedback)
                except ValueError:
                    continue

        # Combine feedback for each line
        combined_feedback = {}
        for line_num, feedbacks in line_feedback.items():
            if len(feedbacks) == 1:
                combined_feedback[line_num] = feedbacks[0]
            else:
                # Combine multiple feedbacks for the same line
                if all(isinstance(f, dict) for f in feedbacks):
                    combined_feedback[line_num] = {
                        "comment": "; ".join(f.get("comment", "") for f in feedbacks),
                        "code": feedbacks[0].get("code")  # Use first code suggestion
                    }
                else:
                    combined_feedback[line_num] = "; ".join(str(f) for f in feedbacks)

        return combined_feedback