import json
import re
from typing import Optional, Any
import os

class Utils:
    # Debug mode flag
    DEBUG_MODE = os.getenv("DEBUG_MODE", "false").lower() == "true"
    
    @staticmethod
    def debug_print(*args, **kwargs):
        if Utils.DEBUG_MODE:
            print(*args, **kwargs)
    
    @staticmethod
    def parse_json_from_response(response: str) -> Optional[dict]:
        response = response.strip()
        # Remove code block markers
        if response.startswith('```json'):
            response = response[len('```json'):].strip()
        if response.startswith('```'):
            response = response[len('```'):].strip()
        if response.endswith('```'):
            response = response[:-3].strip()
        try:
            return json.loads(response)
        except Exception as e:
            print(response)
            print(f"[DEBUG] JSON parse error: {e}")
            return None
    
    @staticmethod
    def extract_file_extension(file_path: str) -> str:
        return file_path.split('.')[-1] if '.' in file_path else ""
    
    @staticmethod
    def is_valid_code_file(file_path: str) -> bool:
        code_extensions = {
            'js', 'ts', 'jsx', 'tsx', 'cs'
        }
        return Utils.extract_file_extension(file_path).lower() in code_extensions
    
    @staticmethod
    def get_file_language_category(file_path: str) -> str:
        extension = Utils.extract_file_extension(file_path).lower()
        
        if extension in {'js', 'ts', 'jsx', 'tsx', 'html', 'css', 'scss'}:
            return "frontend"
        elif extension in {'cs'}:
            return "backend"
        elif extension in {'json', 'yml', 'yaml', 'xml', 'toml', 'ini'}:
            return "config"
        else:
            return "other"
    
    @staticmethod
    def calculate_content_size(content: str) -> int:
        return len(content)
    
    @staticmethod
    def estimate_tokens(content: Any, chars_per_token: int = 4) -> int:
        if isinstance(content, str):
            return len(content) // chars_per_token
        elif isinstance(content, int):
            return content // chars_per_token
        else:
            raise ValueError(f"Content must be string or int, got {type(content)}") 

    @staticmethod
    def detect_code_type(file_path: str) -> str:
        ext = Utils.extract_file_extension(file_path).lower()
        if ext == 'cs':
            return 'csharp'
        if ext in {'tsx', 'jsx'}:
            return 'react'
        if ext == 'js':
            return 'javascript'
        if ext == 'ts':
            if any(file_path.endswith(suffix) for suffix in [
                '.component.ts', '.service.ts', '.module.ts', 
                '.directive.ts', '.pipe.ts', '.guard.ts'
            ]):
                return 'angular'
            return 'typescript'
        return 'unknown' 