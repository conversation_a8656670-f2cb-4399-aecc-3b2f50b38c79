#!/usr/bin/env python3
"""
Simple test with a real diff to test the improved LogicAgent
"""

# Test diff with N+1 query issue
test_diff_with_n_plus_one = """
diff --git a/Backend/Controllers/ProductController.cs b/Backend/Controllers/ProductController.cs
index 1234567..abcdefg 100644
--- a/Backend/Controllers/ProductController.cs
+++ b/Backend/Controllers/ProductController.cs
@@ -1,10 +1,10 @@
 using Microsoft.AspNetCore.Mvc;
 namespace Backend.Controllers {
+    public IActionResult GetProductReviews(int id) {
+        var product = _productRepository.GetById(id);
+        var reviews = new List<Review>();
+        foreach (var reviewId in product.ReviewIds) {
+            reviews.Add(_reviewRepository.GetById(reviewId));
+        }
+        return Ok(reviews);
+    }
 }
"""

# Test diff with good code
test_diff_good_code = """
diff --git a/Backend/Controllers/ProductController.cs b/Backend/Controllers/ProductController.cs
index 1234567..abcdefg 100644
--- a/Backend/Controllers/ProductController.cs
+++ b/Backend/Controllers/ProductController.cs
@@ -1,6 +1,6 @@
 using Microsoft.AspNetCore.Mvc;
 namespace Backend.Controllers {
+    public IActionResult GetProductReviews(int id) {
+        var reviews = _reviewRepository.GetByProductId(id);
+        return Ok(reviews);
+    }
 }
"""

print("Test diffs created:")
print("1. test_diff_with_n_plus_one - Contains N+1 query issue")
print("2. test_diff_good_code - Contains good code")
print("\nYou can now test these in the Streamlit app by running:")
print("streamlit run main.py")
